<?php
/**
 * 授权码生成器
 * 支持多种授权方式：用户、域名、IP、硬件指纹等
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-30
 */

class LicenseGenerator
{
    // 加密密钥
    private $secretKey = 'PbootCMS_License_Key_2025';
    
    // 授权类型常量
    const TYPE_USER = 1;     // 用户授权
    const TYPE_DOMAIN = 2;   // 域名授权
    const TYPE_IP = 3;       // IP授权
    const TYPE_HARDWARE = 4; // 硬件指纹授权
    const TYPE_MIXED = 5;    // 混合授权
    
    /**
     * 生成用户授权码
     * @param string $username 用户名
     * @return string 授权码
     */
    public function generateUserLicense($username)
    {
        if (empty($username)) {
            throw new InvalidArgumentException('用户名不能为空');
        }
        
        // 基于现有算法：SHA1(用户名) → 取前20位 → MD5 → 取第10-19位 → 转大写
        $hash = strtoupper(substr(md5(substr(sha1($username), 0, 20)), 10, 10));
        
        // 添加类型标识和校验码
        $typeFlag = str_pad(dechex(self::TYPE_USER), 2, '0', STR_PAD_LEFT);
        $checksum = $this->generateChecksum($hash . $typeFlag);
        
        return $this->formatLicense($typeFlag . $hash . $checksum);
    }
    
    /**
     * 生成域名授权码
     * @param string $domain 域名
     * @return string 授权码
     */
    public function generateDomainLicense($domain)
    {
        if (empty($domain)) {
            throw new InvalidArgumentException('域名不能为空');
        }
        
        // 清理域名格式
        $domain = strtolower(trim($domain));
        $domain = preg_replace('/^https?:\/\//', '', $domain);
        $domain = preg_replace('/\/.*$/', '', $domain);
        
        // 基于现有算法：SHA1(域名) → 取前10位 → MD5 → 取第10-19位 → 转大写
        $hash = strtoupper(substr(md5(substr(sha1($domain), 0, 10)), 10, 10));
        
        $typeFlag = str_pad(dechex(self::TYPE_DOMAIN), 2, '0', STR_PAD_LEFT);
        $checksum = $this->generateChecksum($hash . $typeFlag);
        
        return $this->formatLicense($typeFlag . $hash . $checksum);
    }
    
    /**
     * 生成IP授权码
     * @param string $ip IP地址
     * @return string 授权码
     */
    public function generateIpLicense($ip)
    {
        if (empty($ip) || !filter_var($ip, FILTER_VALIDATE_IP)) {
            throw new InvalidArgumentException('IP地址格式不正确');
        }
        
        // 基于现有算法：SHA1(IP) → 取前15位 → MD5 → 取第10-19位 → 转大写
        $hash = strtoupper(substr(md5(substr(sha1($ip), 0, 15)), 10, 10));
        
        $typeFlag = str_pad(dechex(self::TYPE_IP), 2, '0', STR_PAD_LEFT);
        $checksum = $this->generateChecksum($hash . $typeFlag);
        
        return $this->formatLicense($typeFlag . $hash . $checksum);
    }
    
    /**
     * 生成硬件指纹授权码
     * @param array $hardwareInfo 硬件信息数组
     * @return string 授权码
     */
    public function generateHardwareLicense($hardwareInfo = [])
    {
        // 获取硬件指纹
        $fingerprint = $this->getHardwareFingerprint($hardwareInfo);
        
        // 使用双重哈希
        $hash = strtoupper(substr(md5(substr(sha1($fingerprint), 0, 25)), 5, 10));
        
        $typeFlag = str_pad(dechex(self::TYPE_HARDWARE), 2, '0', STR_PAD_LEFT);
        $checksum = $this->generateChecksum($hash . $typeFlag);
        
        return $this->formatLicense($typeFlag . $hash . $checksum);
    }
    
    /**
     * 生成混合授权码（支持多种验证方式）
     * @param array $params 参数数组
     * @return string 授权码
     */
    public function generateMixedLicense($params)
    {
        $hashParts = [];
        
        if (!empty($params['username'])) {
            $hashParts[] = substr(sha1($params['username']), 0, 8);
        }
        
        if (!empty($params['domain'])) {
            $domain = strtolower(preg_replace('/^https?:\/\//', '', $params['domain']));
            $hashParts[] = substr(sha1($domain), 0, 8);
        }
        
        if (!empty($params['ip'])) {
            $hashParts[] = substr(sha1($params['ip']), 0, 8);
        }
        
        if (empty($hashParts)) {
            throw new InvalidArgumentException('至少需要提供一种授权参数');
        }
        
        $combinedHash = implode('', $hashParts);
        $hash = strtoupper(substr(md5($combinedHash), 10, 10));
        
        $typeFlag = str_pad(dechex(self::TYPE_MIXED), 2, '0', STR_PAD_LEFT);
        $checksum = $this->generateChecksum($hash . $typeFlag);
        
        return $this->formatLicense($typeFlag . $hash . $checksum);
    }
    
    /**
     * 生成批量授权码
     * @param array $items 授权项目数组
     * @param int $type 授权类型
     * @return string Base64编码的授权码
     */
    public function generateBatchLicense($items, $type = self::TYPE_MIXED)
    {
        if (empty($items) || !is_array($items)) {
            throw new InvalidArgumentException('授权项目不能为空');
        }
        
        $licenses = [];
        foreach ($items as $item) {
            switch ($type) {
                case self::TYPE_USER:
                    $licenses[] = $this->generateUserLicense($item);
                    break;
                case self::TYPE_DOMAIN:
                    $licenses[] = $this->generateDomainLicense($item);
                    break;
                case self::TYPE_IP:
                    $licenses[] = $this->generateIpLicense($item);
                    break;
                default:
                    throw new InvalidArgumentException('不支持的批量授权类型');
            }
        }
        
        // 组合授权码：授权列表/用户信息，然后Base64编码
        $licenseData = implode(',', $licenses) . '/' . (isset($items[0]) ? $items[0] : 'batch_user');
        return base64_encode($licenseData) . 'L'; // 添加标识符
    }
    
    /**
     * 获取硬件指纹
     * @param array $hardwareInfo 硬件信息
     * @return string 硬件指纹
     */
    private function getHardwareFingerprint($hardwareInfo = [])
    {
        $fingerprint = [];
        
        // 如果提供了硬件信息，使用提供的信息
        if (!empty($hardwareInfo)) {
            foreach ($hardwareInfo as $key => $value) {
                $fingerprint[] = $key . ':' . $value;
            }
        } else {
            // 自动获取系统信息
            $fingerprint[] = 'os:' . PHP_OS;
            $fingerprint[] = 'php:' . PHP_VERSION;
            
            // 尝试获取更多系统信息
            if (function_exists('gethostname')) {
                $fingerprint[] = 'host:' . gethostname();
            }
            
            if (isset($_SERVER['SERVER_SOFTWARE'])) {
                $fingerprint[] = 'server:' . $_SERVER['SERVER_SOFTWARE'];
            }
            
            if (isset($_SERVER['HTTP_USER_AGENT'])) {
                $fingerprint[] = 'ua:' . substr(md5($_SERVER['HTTP_USER_AGENT']), 0, 8);
            }
        }
        
        return implode('|', $fingerprint);
    }
    
    /**
     * 生成校验码
     * @param string $data 数据
     * @return string 校验码
     */
    private function generateChecksum($data)
    {
        return substr(md5($data . $this->secretKey), 0, 4);
    }
    
    /**
     * 格式化授权码
     * @param string $license 原始授权码
     * @return string 格式化后的授权码
     */
    private function formatLicense($license)
    {
        // 每4位添加一个连字符
        return strtoupper(chunk_split($license, 4, '-'));
    }
    
    /**
     * 设置加密密钥
     * @param string $key 密钥
     */
    public function setSecretKey($key)
    {
        $this->secretKey = $key;
    }
    
    /**
     * 获取授权类型名称
     * @param int $type 类型代码
     * @return string 类型名称
     */
    public static function getTypeName($type)
    {
        $types = [
            self::TYPE_USER => '用户授权',
            self::TYPE_DOMAIN => '域名授权',
            self::TYPE_IP => 'IP授权',
            self::TYPE_HARDWARE => '硬件授权',
            self::TYPE_MIXED => '混合授权'
        ];
        
        return isset($types[$type]) ? $types[$type] : '未知类型';
    }
}
